import { NextApiRequest, NextApiResponse } from 'next';

import { SiteCartShippingApiResponse } from '~/data/models/SiteCartShippingResponse';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import {
  backendCreateCartShipping,
  backendDeleteCartShipping,
  backendGetCartShipping,
  backendUpdateCartShipping,
} from '~/lib/backend/checkout/cart-shipping';
import { ErrorResponse } from '~/lib/fetch/index.types';
import { getStringifiedParams } from '~/lib/utils/routes';

const cartShippingApi = async (
  request: NextApiRequest,
  response: NextApiResponse<SiteCartShippingApiResponse | ErrorResponse>,
) => {
  backendBootstrap({ request });
  const { cartId, ...rest } = getStringifiedParams(request.query);

  if (!cartId) {
    response.status(400).end();
    return;
  }

  switch (request.method?.toLowerCase()) {
    case 'get': {
      const res = await backendGetCartShipping(
        { cartId, query: rest },
        request,
      );

      if (res.isSuccess) {
        return response.json(res.data);
      }

      if (res.error.statusCode !== 404 && res.error.statusCode !== 539) {
        return response.status(res.error.statusCode).end();
      } else {
        return response
          .status(res.error.statusCode)
          .json({ error: res.error, isSuccess: false });
      }
    }
    case 'post': {
      const res = await backendCreateCartShipping(
        {
          cartId,
          input: request.body,
          query: rest,
        },
        request,
      );

      if (res.isSuccess) {
        response.json(res.data);
        return;
      }

      return response.status(res.error.statusCode).end();
    }
    case 'put': {
      const res = await backendUpdateCartShipping(
        {
          cartId,
          input: request.body,
          query: rest,
        },
        request,
      );

      if (res.isSuccess) {
        response.json(res.data);
        return;
      }

      return response.status(res.error.statusCode).end();
    }
    case 'delete': {
      const res = await backendDeleteCartShipping(
        {
          cartId,
          query: rest,
        },
        request,
      );

      if (res.isSuccess) {
        response.status(204).end();
        return;
      }
      return response.status(res.error.statusCode).end();
    }
    default:
      response.status(400).end();
      break;
  }
};

export default cartShippingApi;
