import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import { SiteCartShippingApiResponse } from '~/data/models/SiteCartShippingResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';
import { ApiRequest } from '~/lib/fetch-backend/index.types';

export async function backendGetCartShipping(
  {
    cartId,
    query,
  }: {
    cartId: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartShippingApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    extraQueryParams,
    method: 'get',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendDeleteCartShipping(
  {
    cartId,
    query,
  }: {
    cartId: string;
    query?: Record<string, string>;
  },
  request?: ApiRequest,
) {
  return await fetchWithErrorHandling<SiteCartShippingApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    method: 'delete',
    params: {
      cartId,
    },
    query,
    request,
  });
}

export async function backendUpdateCartShipping(
  {
    cartId,
    query,
    input,
  }: {
    cartId: string;
    input: SiteCartShippingRequest;
    query?: Record<string, string>;
  },
  request?: ApiRequest,
) {
  return await fetchWithErrorHandling<
    SiteCartShippingApiResponse,
    SiteCartShippingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'put',
    params: {
      cartId,
    },
    query,
    request,
  });
}

export async function backendCreateCartShipping(
  {
    cartId,
    query,
    input,
  }: {
    cartId: string;
    input: SiteCartShippingRequest;
    query?: Record<string, string>;
  },
  request?: ApiRequest,
) {
  return await fetchWithErrorHandling<
    SiteCartShippingApiResponse,
    SiteCartShippingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
    query,
    request,
  });
}
